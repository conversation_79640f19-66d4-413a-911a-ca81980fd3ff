1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bm.atool"
4    android:versionCode="1"
5    android:versionName="1.0.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-feature
11-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:6:5-8:36
12        android:name="android.hardware.telephony"
12-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:7:9-50
13        android:required="false" />
13-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:8:9-33
14
15    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
15-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:10:5-11:47
15-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:10:22-83
16    <uses-permission android:name="android.permission.RECEIVE_SMS" />
16-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:12:5-70
16-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:12:22-67
17    <uses-permission android:name="android.permission.READ_SMS" />
17-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:13:5-67
17-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:13:22-64
18    <uses-permission android:name="android.permission.SEND_SMS" />
18-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:14:5-67
18-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:14:22-64
19    <uses-permission android:name="android.permission.WRITE_SMS" />
19-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-68
19-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-65
20    <uses-permission android:name="android.permission.INTERNET" />
20-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
20-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
21    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
21-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:17:5-75
21-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:17:22-72
22    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
22-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:18:5-76
22-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:18:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:19:5-77
23-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:19:22-74
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:20:5-68
24-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:20:22-65
25
26    <permission android:name="android.permission.DEVICE_POWER" />
26-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:21:5-22:51
26-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:21:17-63
27
28    <uses-permission android:name="android.permission.BIND_JOB_SERVICE" />
28-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:23:5-24:47
28-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:23:22-72
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:25:5-79
29-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:25:22-76
30    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
30-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:26:5-79
30-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:26:22-76
31    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
31-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:27:5-76
31-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:27:22-73
32    <uses-permission android:name="android.permission.INTERNET" />
32-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
32-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
33    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:29:5-80
33-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:29:22-77
34    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
34-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:30:5-77
34-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:30:22-74
35    <uses-permission android:name="android.permission.WRITE_MEDIA_IMAGES" />
35-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:31:5-77
35-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:31:22-74
36    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
36-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:32:5-79
36-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:32:22-76
37    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
37-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:33:5-81
37-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:33:22-78
38    <uses-permission android:name="android.permission.INTERNET" />
38-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
38-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
39-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:35:5-92
39-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:35:22-89
40    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
40-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:36:5-95
40-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:36:22-92
41    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
41-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:37:5-77
41-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:37:22-75
42    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
42-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:38:5-39:47
42-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:38:22-82
43    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
43-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:40:5-80
43-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:40:22-78
44
45    <permission
45-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:41:5-192:19
52        android:name="com.bm.atool.App"
52-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:51:9-28
53        android:allowBackup="true"
53-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:42:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:43:9-65
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:fullBackupContent="@xml/backup_rules"
58-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:44:9-54
59        android:icon="@mipmap/ic_launcher"
59-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:45:9-43
60        android:label="@string/app_name"
60-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:46:9-41
61        android:roundIcon="@mipmap/ic_launcher_round"
61-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:47:9-54
62        android:supportsRtl="true"
62-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:48:9-35
63        android:testOnly="true"
64        android:theme="@style/Theme.AndroidTool"
64-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:49:9-49
65        android:usesCleartextTraffic="true" >
65-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:50:9-44
66        <activity
66-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:53:9-55:40
67            android:name="com.bm.atool.LoginActivity"
67-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:54:13-42
68            android:exported="false" />
68-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:55:13-37
69        <activity
69-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:56:9-69:20
70            android:name="com.bm.atool.MainActivity"
70-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:57:13-41
71            android:excludeFromRecents="true"
71-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:63:13-46
72            android:exported="true"
72-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:58:13-36
73            android:label="@string/app_name"
73-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:59:13-45
74            android:launchMode="singleTask"
74-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:61:13-44
75            android:taskAffinity=""
75-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:62:13-36
76            android:theme="@style/Theme.AndroidTool" >
76-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:60:13-53
77            <intent-filter>
77-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:64:13-68:29
78                <action android:name="android.intent.action.MAIN" />
78-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:65:17-69
78-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:65:25-66
79
80                <category android:name="android.intent.category.LAUNCHER" />
80-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:67:17-77
80-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:67:27-74
81            </intent-filter>
82        </activity>
83        <activity
83-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:70:9-72:39
84            android:name="com.bm.atool.service.singlepixel.SinglePixelActivity"
84-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:71:13-68
85            android:exported="true" />
85-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:72:13-36
86
87        <receiver
87-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:74:9-80:20
88            android:name="com.bm.atool.receivers.SimChangedReceiver"
88-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:75:13-57
89            android:exported="true" >
89-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:76:13-36
90            <intent-filter>
90-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:77:13-79:29
91                <action android:name="android.intent.action.SIM_STATE_CHANGED" />
91-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:78:17-81
91-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:78:25-79
92            </intent-filter>
93        </receiver>
94        <receiver
94-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:81:9-87:20
95            android:name="com.bm.atool.receivers.SmsReceiver"
95-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:81:19-56
96            android:exported="true"
96-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:82:13-36
97            android:permission="android.permission.BROADCAST_SMS" >
97-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:83:13-66
98            <intent-filter>
98-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:84:13-86:29
99                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
99-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:85:17-81
99-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:85:25-79
100            </intent-filter>
101        </receiver>
102        <receiver
102-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:89:9-102:20
103            android:name="com.bm.atool.receivers.WakeUpReceiver"
103-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:90:13-53
104            android:exported="true"
104-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:92:13-36
105            android:process=":watch" >
105-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:91:13-37
106            <intent-filter>
106-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:93:13-101:29
107                <action android:name="android.intent.action.USER_PRESENT" />
107-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:94:17-76
107-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:94:25-74
108                <action android:name="android.intent.action.BOOT_COMPLETED" />
108-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:95:17-79
108-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:95:25-76
109                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
109-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:96:17-79
109-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:96:25-76
110                <action android:name="android.intent.action.USER_PRESENT" />
110-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:94:17-76
110-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:94:25-74
111                <action android:name="android.intent.action.MEDIA_MOUNTED" />
111-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:98:17-78
111-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:98:25-75
112                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
112-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:99:17-87
112-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:99:25-84
113                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
113-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:100:17-90
113-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:100:25-87
114            </intent-filter>
115        </receiver>
116        <receiver
116-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:103:9-130:20
117            android:name="com.bm.atool.receivers.WakeUpAutoStartReceiver"
117-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:104:13-62
118            android:exported="true"
118-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:106:13-36
119            android:process=":watch" >
119-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:105:13-37
120
121            <!-- 手机启动 -->
122            <intent-filter>
122-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:108:13-111:29
123                <action android:name="android.intent.action.BOOT_COMPLETED" />
123-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:95:17-79
123-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:95:25-76
124                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
124-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:96:17-79
124-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:96:25-76
125            </intent-filter>
126            <!-- 软件安装卸载 -->
127            <intent-filter>
127-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:113:13-117:29
128                <action android:name="android.intent.action.PACKAGE_ADDED" />
128-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:114:17-77
128-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:114:25-75
129                <action android:name="android.intent.action.PACKAGE_REMOVED" />
129-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:115:17-79
129-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:115:25-77
130
131                <data android:scheme="package" />
131-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:116:17-49
131-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:116:23-47
132            </intent-filter>
133            <!-- 网络监听 -->
134            <intent-filter>
134-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:119:13-123:29
135                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
135-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:96:17-79
135-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:96:25-76
136                <action android:name="android.net.wifi.WIFI_STATE_CJANGED" />
136-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:121:17-77
136-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:121:25-75
137                <action android:name="android.net.wifi.STATE_CHANGE" />
137-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:122:17-71
137-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:122:25-69
138            </intent-filter>
139            <!-- 文件挂载 -->
140            <intent-filter>
140-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:125:13-129:29
141                <action android:name="android.intent.action.MEDIA_EJECT" />
141-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:126:17-75
141-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:126:25-73
142                <action android:name="android.intent.action.MEDIA_MOUNTED" />
142-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:98:17-78
142-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:98:25-75
143
144                <data android:scheme="file" />
144-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:116:17-49
144-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:116:23-47
145            </intent-filter>
146        </receiver>
147
148        <!-- 守护进程 watch -->
149        <service
149-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:133:9-138:43
150            android:name="com.bm.atool.service.JobSchedulerService"
150-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:134:13-56
151            android:enabled="true"
151-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:136:13-35
152            android:exported="true"
152-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:137:13-36
153            android:permission="android.permission.BIND_JOB_SERVICE"
153-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:135:13-69
154            android:process=":watch_job" />
154-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:138:13-41
155        <service
155-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:140:9-145:43
156            android:name="com.bm.atool.service.WatchDogService"
156-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:141:13-52
157            android:enabled="true"
157-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:143:13-35
158            android:exported="true"
158-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:144:13-36
159            android:foregroundServiceType="mediaPlayback"
159-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:142:13-58
160            android:process=":watch_dog" />
160-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:145:13-41
161        <service
161-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:147:9-149:46
162            android:name="com.bm.atool.service.PlayMusicService"
162-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:147:18-58
163            android:foregroundServiceType="mediaPlayback"
163-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:148:13-58
164            android:process=":watch_player" />
164-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:149:13-44
165        <service
165-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:150:9-163:19
166            android:name="com.bm.atool.service.ANTAccessibilityService"
166-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:151:13-60
167            android:enabled="true"
167-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:152:13-35
168            android:exported="true"
168-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:153:13-36
169            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
169-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:155:13-79
170            android:process=":accessibility" >
170-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:154:13-45
171            <intent-filter>
171-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:156:13-158:29
172                <action android:name="android.accessibilityservice.AccessibilityService" />
172-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:157:17-92
172-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:157:25-89
173            </intent-filter>
174
175            <meta-data
175-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:160:13-162:54
176                android:name="android.accessibilityservice"
176-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:161:17-60
177                android:resource="@xml/allocation" />
177-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:162:17-51
178        </service>
179        <service
179-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:164:9-180:19
180            android:name="com.bm.atool.service.SocketService"
180-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:165:13-50
181            android:exported="false"
181-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:166:13-37
182            android:label="SocketService"
182-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:167:13-42
183            android:permission="android.permission.BIND_VPN_SERVICE"
183-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:169:13-69
184            android:process=":socket" >
184-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:168:13-38
185            <intent-filter>
185-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:170:13-172:29
186                <action android:name="android.net.VpnService" />
186-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:171:17-65
186-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:171:25-62
187            </intent-filter>
188
189            <meta-data
189-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:173:13-175:39
190                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
190-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:174:17-73
191                android:value="true" />
191-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:175:17-37
192
193            <property
193-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:176:13-178:38
194                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
194-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:177:17-76
195                android:value="vpn" />
195-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:178:17-36
196        </service>
197        <service
197-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:182:9-190:19
198            android:name="com.bm.atool.service.NotificationService"
198-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:183:13-56
199            android:exported="true"
199-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:186:13-36
200            android:label="@string/app_name"
200-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:184:13-45
201            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
201-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:185:13-87
202            <intent-filter>
202-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:187:13-189:29
203                <action android:name="android.service.notification.NotificationListenerService" />
203-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:188:17-99
203-->E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:188:25-96
204            </intent-filter>
205        </service>
206
207        <provider
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
208            android:name="androidx.startup.InitializationProvider"
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
209            android:authorities="com.bm.atool.androidx-startup"
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
210            android:exported="false" >
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
211            <meta-data
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
212                android:name="androidx.emoji2.text.EmojiCompatInitializer"
212-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
213                android:value="androidx.startup" />
213-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
214            <meta-data
214-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
215                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
215-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
216                android:value="androidx.startup" />
216-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
217            <meta-data
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
218                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
219                android:value="androidx.startup" />
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
220        </provider>
221
222        <uses-library
222-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
223            android:name="androidx.window.extensions"
223-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
224            android:required="false" />
224-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
225        <uses-library
225-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
226            android:name="androidx.window.sidecar"
226-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
227            android:required="false" />
227-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
228
229        <receiver
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
230            android:name="androidx.profileinstaller.ProfileInstallReceiver"
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
231            android:directBootAware="false"
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
232            android:enabled="true"
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
233            android:exported="true"
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
234            android:permission="android.permission.DUMP" >
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
235            <intent-filter>
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
236                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
237            </intent-filter>
238            <intent-filter>
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
239                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
240            </intent-filter>
241            <intent-filter>
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
242                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
243            </intent-filter>
244            <intent-filter>
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
245                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
246            </intent-filter>
247        </receiver>
248    </application>
249
250</manifest>
