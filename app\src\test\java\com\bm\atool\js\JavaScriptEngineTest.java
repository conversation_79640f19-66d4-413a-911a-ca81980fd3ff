package com.bm.atool.js;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * JavaScript引擎测试类
 * 测试JavaScript引擎的基本功能（不依赖Android环境）
 */
public class JavaScriptEngineTest {

    @Test
    public void testAndroidBridgeInterfaceExists() {
        // 测试AndroidBridgeInterface接口是否存在
        try {
            Class<?> interfaceClass = AndroidBridgeInterface.class;
            assertTrue("AndroidBridgeInterface应该是接口", interfaceClass.isInterface());

            // 检查接口方法
            assertNotNull("log方法应该存在", interfaceClass.getMethod("log", String.class, String.class));
            assertNotNull("getCurrentTimestamp方法应该存在", interfaceClass.getMethod("getCurrentTimestamp"));
            assertNotNull("getDeviceInfo方法应该存在", interfaceClass.getMethod("getDeviceInfo"));

        } catch (Exception e) {
            fail("AndroidBridgeInterface接口测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testAndroidBridgeImplementation() {
        // 测试AndroidBridge是否正确实现了接口
        try {
            AndroidBridge bridge = new AndroidBridge(null, null);
            assertTrue("AndroidBridge应该实现AndroidBridgeInterface",
                      bridge instanceof AndroidBridgeInterface);

            // 测试接口方法是否可调用
            long timestamp = bridge.getCurrentTimestamp();
            assertTrue("时间戳应该大于0", timestamp > 0);

        } catch (Exception e) {
            fail("AndroidBridge实现测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testInterfaceMethodSignatures() {
        // 测试接口方法签名
        try {
            Class<?> interfaceClass = AndroidBridgeInterface.class;

            // 检查关键方法的返回类型
            assertEquals("log方法返回类型应该是void",
                        void.class, interfaceClass.getMethod("log", String.class, String.class).getReturnType());
            assertEquals("getCurrentTimestamp方法返回类型应该是long",
                        long.class, interfaceClass.getMethod("getCurrentTimestamp").getReturnType());
            assertEquals("getDeviceInfo方法返回类型应该是String",
                        String.class, interfaceClass.getMethod("getDeviceInfo").getReturnType());

        } catch (Exception e) {
            fail("接口方法签名测试失败: " + e.getMessage());
        }
    }
}
