package com.bm.atool.js;

import android.content.Context;
import android.util.Log;

import app.cash.quickjs.QuickJs;

import com.bm.atool.SmsSender;
import com.bm.atool.UssdProcessor;
import com.bm.atool.Sys;
import com.bm.atool.model.SendSmsRequest;
import com.bm.atool.model.UssdRequest;
import com.bm.atool.utils.PhoneUtils;
import com.google.gson.Gson;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import io.socket.client.Socket;

/**
 * JavaScript引擎管理器
 * 负责管理QuickJS引擎的生命周期、脚本执行、错误处理等核心功能
 */
public class JavaScriptEngine {
    private static final String TAG = "JavaScriptEngine";
    private static final int SCRIPT_TIMEOUT_SECONDS = 30;
    
    private QuickJs quickJs;
    private Context context;
    private Socket socket;
    private ExecutorService executorService;
    private Gson gson;
    
    public JavaScriptEngine(Context context, Socket socket) {
        this.context = context;
        this.socket = socket;
        this.executorService = Executors.newSingleThreadExecutor();
        this.gson = new Gson();
        initializeEngine();
    }
    
    /**
     * 初始化JavaScript引擎
     */
    private void initializeEngine() {
        try {
            quickJs = QuickJs.create();
            setupNativeBindings();
            Log.i(TAG, "JavaScript引擎初始化成功");
        } catch (Exception e) {
            Log.e(TAG, "JavaScript引擎初始化失败", e);
            throw new RuntimeException("Failed to initialize JavaScript engine", e);
        }
    }
    
    /**
     * 设置原生绑定
     */
    private void setupNativeBindings() {
        // 绑定Android原生功能到JavaScript环境
        quickJs.set("Android", AndroidBridge.class, new AndroidBridge(context, socket));
        
        // 绑定日志功能
        quickJs.evaluate("" +
            "var console = {" +
            "  log: function(msg) { Android.log('INFO', String(msg)); }," +
            "  error: function(msg) { Android.log('ERROR', String(msg)); }," +
            "  warn: function(msg) { Android.log('WARN', String(msg)); }," +
            "  debug: function(msg) { Android.log('DEBUG', String(msg)); }" +
            "};"
        );
        
        // 绑定SMS功能
        quickJs.evaluate("" +
            "var SMS = {" +
            "  send: function(to, content, targetPhone) {" +
            "    return Android.sendSms(to, content, targetPhone || null);" +
            "  }," +
            "  clear: function(fromNumber) {" +
            "    return Android.clearSms(fromNumber || null);" +
            "  }" +
            "};"
        );
        
        // 绑定USSD功能
        quickJs.evaluate("" +
            "var USSD = {" +
            "  execute: function(code, targetPhone, timeout) {" +
            "    return Android.executeUssd(code, targetPhone || null, timeout || 15);" +
            "  }" +
            "};"
        );
        
        // 绑定系统信息功能
        quickJs.evaluate("" +
            "var System = {" +
            "  getDeviceInfo: function() {" +
            "    return Android.getDeviceInfo();" +
            "  }," +
            "  getPhoneNumbers: function() {" +
            "    return Android.getPhoneNumbers();" +
            "  }," +
            "  getBatteryLevel: function() {" +
            "    return Android.getBatteryLevel();" +
            "  }" +
            "};"
        );
        
        Log.d(TAG, "原生绑定设置完成");
    }
    
    /**
     * 执行JavaScript脚本
     */
    public ScriptResult executeScript(String script) {
        return executeScript(script, SCRIPT_TIMEOUT_SECONDS);
    }
    
    /**
     * 执行JavaScript脚本（带超时）
     */
    public ScriptResult executeScript(String script, int timeoutSeconds) {
        if (quickJs == null) {
            return new ScriptResult(false, null, "JavaScript引擎未初始化");
        }
        
        Future<ScriptResult> future = executorService.submit(() -> {
            try {
                Log.d(TAG, "开始执行JavaScript脚本: " + script.substring(0, Math.min(script.length(), 100)) + "...");
                Object result = quickJs.evaluate(script);
                String resultStr = result != null ? result.toString() : "null";
                Log.d(TAG, "脚本执行成功，结果: " + resultStr);
                return new ScriptResult(true, resultStr, null);
            } catch (Exception e) {
                Log.e(TAG, "脚本执行失败", e);
                return new ScriptResult(false, null, e.getMessage());
            }
        });
        
        try {
            return future.get(timeoutSeconds, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            future.cancel(true);
            Log.e(TAG, "脚本执行超时");
            return new ScriptResult(false, null, "脚本执行超时");
        } catch (Exception e) {
            Log.e(TAG, "脚本执行异常", e);
            return new ScriptResult(false, null, e.getMessage());
        }
    }
    
    /**
     * 执行JavaScript函数
     */
    public ScriptResult executeFunction(String functionName, Object... args) {
        if (quickJs == null) {
            return new ScriptResult(false, null, "JavaScript引擎未初始化");
        }
        
        try {
            StringBuilder argsStr = new StringBuilder();
            for (int i = 0; i < args.length; i++) {
                if (i > 0) argsStr.append(", ");
                if (args[i] instanceof String) {
                    argsStr.append("'").append(args[i].toString().replace("'", "\\'")).append("'");
                } else {
                    argsStr.append(gson.toJson(args[i]));
                }
            }
            
            String script = functionName + "(" + argsStr.toString() + ");";
            return executeScript(script);
        } catch (Exception e) {
            Log.e(TAG, "执行函数失败: " + functionName, e);
            return new ScriptResult(false, null, e.getMessage());
        }
    }
    
    /**
     * 加载JavaScript库或模块
     */
    public ScriptResult loadScript(String script) {
        return executeScript(script);
    }
    
    /**
     * 重置JavaScript引擎
     */
    public void reset() {
        try {
            if (quickJs != null) {
                quickJs.close();
            }
            initializeEngine();
            Log.i(TAG, "JavaScript引擎重置成功");
        } catch (Exception e) {
            Log.e(TAG, "JavaScript引擎重置失败", e);
        }
    }
    
    /**
     * 关闭JavaScript引擎
     */
    public void close() {
        try {
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            }
            
            if (quickJs != null) {
                quickJs.close();
                quickJs = null;
            }
            
            Log.i(TAG, "JavaScript引擎已关闭");
        } catch (Exception e) {
            Log.e(TAG, "关闭JavaScript引擎时出错", e);
        }
    }
    
    /**
     * 检查引擎是否可用
     */
    public boolean isAvailable() {
        return quickJs != null;
    }
    
    /**
     * 脚本执行结果
     */
    public static class ScriptResult {
        public final boolean success;
        public final String result;
        public final String error;
        
        public ScriptResult(boolean success, String result, String error) {
            this.success = success;
            this.result = result;
            this.error = error;
        }
        
        @Override
        public String toString() {
            if (success) {
                return "Success: " + result;
            } else {
                return "Error: " + error;
            }
        }
    }
}
