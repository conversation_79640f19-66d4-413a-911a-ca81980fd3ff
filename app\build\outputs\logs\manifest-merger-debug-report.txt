-- Merging decision tree log ---
manifest
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-194:12
INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-194:12
INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-194:12
INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-194:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\c78b8bb97859eafdefc774f33c8b74d3\transformed\viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5f8dd2566c9eda203a98668cc7ef7b6c\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\0cfedff09b8c1a61564a2d292f981376\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\9b35a196b101b8bacc2d18286d5f5bc2\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\0dd6b178300a398c5583e81b52d2ca55\transformed\navigation-ui-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\065662a114d9e980de2eb8602aa46dfe\transformed\material-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3ee4fad182311aafa4dd3c21e1a92920\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f99e54b54e0fd99cba6c075cd2f82e8\transformed\preference-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\58d2da51b3a7297353dc91fcce17a305\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\80aca24db86cd42a34c9c5f0872fe864\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c520583edb1b643c07b55b9673b3d860\transformed\safe-parcel-1.7.0\AndroidManifest.xml:6:1-14:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c29a2a5ec5a82e9ac89894f30961df45\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d968e2e921fe73fd429dc1484158ee01\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d2877f2a4374c5f0ea495c4307113570\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\234d130502bfda745d1f0ed467eb316b\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\cd43725c0a9e9dcf8a29cd2176ebb49f\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\ee8b137033d54aa057823e70d615e647\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\2918f0ebf0c103056ddddb3751eae9f0\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\eced6a23faf4beccaf4d548a6076d331\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\59d7b4103fe863e83f5cfb6d5ff9052e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b57708684cd122ed48bec8d25a938f16\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac11b2842053e1c378129bd733121fd7\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\eb0dcaade8c7dcad314192e8fa45f6b0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e4efd8f3ae2750354e81a0a9c11c89eb\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c0c7ad1cec94fe321cef48655533f965\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\62bb95ec0abc6811b0683a9ec0dfc95b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\06b7d36fc1385742e8e5e7ec844582ed\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c07ac8b948bf0d861a502c28c7b3c9ef\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1bcc2a33a5dad0ef3e775f681fcdb8be\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\e2bc52ec53d3d09eacd7afda93ce0878\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\d69ae89571b53efad2154ad8afbb02d7\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4786fc9597915b750c9674f8019f3638\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc7970ebcc9b9f99b26feebcc6a8913f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e42ed84a4862a4d03c59d5391f7980cb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31637ce5e5872ca525b4fe826e027ef\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\62f38e60f2e215177108a8ff7d00958d\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2db60f4eb854c2f606a4cbc7486eff43\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c0235d1d2d6a040a29e091281f1d0c3a\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\93a1855047dd69a012ffb1466c3baec5\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\387903bd7373a42a8601c75ca13d7224\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e92d0134cc811c88c1672a7b95d4b00d\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.tiann:FreeReflection:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\dc19d8e7ad6666734d2bbefc174935c3\transformed\FreeReflection-3.2.2\AndroidManifest.xml:2:1-9:12
MERGED from [app.cash.quickjs:quickjs-android:0.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\e000a505335212ee8a988555e4104d45\transformed\quickjs-android-0.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\7b3ddaef24068abceaf1b8691f09de43\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d9d9cadd3455c73332883d4d92e61ba\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8063df366648b779671febaa29771aa6\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\eed3683499b10c60b5c9c6e2a518c54d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0020c061f6caf35e73f20a352bf6cb72\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0845b252e397a1fbf50c047ea290ff7d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\62e6ae9dec43c760cbb39d5d416adced\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\93f78976c09f31387e04ef9100b48ea2\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\50743c170165225d5979ca5903d33e9b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f40bd75703256c0d3b710b9eeff99b92\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c9a64bbeebbcaee8a717efe3ff041abc\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:4:5-27
		INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.telephony
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:6:5-8:36
	android:required
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:8:9-33
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:7:9-50
uses-permission#android.permission.READ_PRIVILEGED_PHONE_STATE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:10:5-11:47
	tools:ignore
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:11:9-44
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:10:22-83
uses-permission#android.permission.RECEIVE_SMS
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:12:5-70
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:12:22-67
uses-permission#android.permission.READ_SMS
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:13:5-67
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:13:22-64
uses-permission#android.permission.SEND_SMS
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:14:5-67
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:14:22-64
uses-permission#android.permission.WRITE_SMS
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-68
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-65
uses-permission#android.permission.INTERNET
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.READ_PHONE_STATE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:17:5-75
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:17:22-72
uses-permission#android.permission.READ_PHONE_NUMBERS
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:18:5-76
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:18:22-74
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:19:5-77
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:19:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:20:5-68
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:20:22-65
permission#android.permission.DEVICE_POWER
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:21:5-22:51
	tools:ignore
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:22:9-48
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:21:17-63
uses-permission#android.permission.BIND_JOB_SERVICE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:23:5-24:47
	tools:ignore
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:24:9-44
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:23:22-72
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:25:5-79
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:25:22-76
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:26:5-79
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:27:5-76
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:27:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:29:5-80
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:29:22-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:30:5-77
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:30:22-74
uses-permission#android.permission.WRITE_MEDIA_IMAGES
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:31:5-77
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:31:22-74
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:32:5-79
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:32:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:33:5-81
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:33:22-78
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:35:5-92
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:35:22-89
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:36:5-95
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:36:22-92
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:37:5-77
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:37:22-75
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:38:5-39:47
	tools:ignore
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:39:9-44
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:38:22-82
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:40:5-80
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:40:22-78
application
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:41:5-192:19
INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:41:5-192:19
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\065662a114d9e980de2eb8602aa46dfe\transformed\material-1.5.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\065662a114d9e980de2eb8602aa46dfe\transformed\material-1.5.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3ee4fad182311aafa4dd3c21e1a92920\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3ee4fad182311aafa4dd3c21e1a92920\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\eed3683499b10c60b5c9c6e2a518c54d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\eed3683499b10c60b5c9c6e2a518c54d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\62e6ae9dec43c760cbb39d5d416adced\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\62e6ae9dec43c760cbb39d5d416adced\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:48:9-35
	android:label
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:46:9-41
	android:fullBackupContent
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:44:9-54
	android:roundIcon
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:47:9-54
	tools:targetApi
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:52:9-29
	android:icon
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:45:9-43
	android:allowBackup
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:42:9-35
	android:theme
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:49:9-49
	android:dataExtractionRules
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:43:9-65
	android:usesCleartextTraffic
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:50:9-44
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:51:9-28
activity#com.bm.atool.LoginActivity
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:53:9-55:40
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:55:13-37
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:54:13-42
activity#com.bm.atool.MainActivity
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:56:9-69:20
	android:label
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:59:13-45
	android:excludeFromRecents
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:63:13-46
	android:launchMode
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:61:13-44
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:58:13-36
	android:theme
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:60:13-53
	android:taskAffinity
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:62:13-36
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:57:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:64:13-68:29
action#android.intent.action.MAIN
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:65:17-69
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:65:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:67:17-77
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:67:27-74
activity#com.bm.atool.service.singlepixel.SinglePixelActivity
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:70:9-72:39
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:72:13-36
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:71:13-68
receiver#com.bm.atool.receivers.SimChangedReceiver
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:74:9-80:20
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:76:13-36
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:75:13-57
intent-filter#action:name:android.intent.action.SIM_STATE_CHANGED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:77:13-79:29
action#android.intent.action.SIM_STATE_CHANGED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:78:17-81
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:78:25-79
receiver#com.bm.atool.receivers.SmsReceiver
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:81:9-87:20
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:82:13-36
	android:permission
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:83:13-66
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:81:19-56
intent-filter#action:name:android.provider.Telephony.SMS_RECEIVED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:84:13-86:29
action#android.provider.Telephony.SMS_RECEIVED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:85:17-81
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:85:25-79
receiver#com.bm.atool.receivers.WakeUpReceiver
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:89:9-102:20
	android:process
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:91:13-37
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:92:13-36
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:90:13-53
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED+action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MEDIA_MOUNTED+action:name:android.intent.action.USER_PRESENT+action:name:android.intent.action.USER_PRESENT+action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:93:13-101:29
action#android.intent.action.USER_PRESENT
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:94:17-76
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:94:25-74
action#android.intent.action.BOOT_COMPLETED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:95:17-79
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:95:25-76
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:96:17-79
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:96:25-76
action#android.intent.action.MEDIA_MOUNTED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:98:17-78
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:98:25-75
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:99:17-87
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:99:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:100:17-90
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:100:25-87
receiver#com.bm.atool.receivers.WakeUpAutoStartReceiver
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:103:9-130:20
	android:process
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:105:13-37
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:106:13-36
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:104:13-62
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:108:13-111:29
intent-filter#action:name:android.intent.action.PACKAGE_ADDED+action:name:android.intent.action.PACKAGE_REMOVED+data:scheme:package
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:113:13-117:29
action#android.intent.action.PACKAGE_ADDED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:114:17-77
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:114:25-75
action#android.intent.action.PACKAGE_REMOVED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:115:17-79
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:115:25-77
data
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:116:17-49
	android:scheme
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:116:23-47
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE+action:name:android.net.wifi.STATE_CHANGE+action:name:android.net.wifi.WIFI_STATE_CJANGED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:119:13-123:29
action#android.net.wifi.WIFI_STATE_CJANGED
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:121:17-77
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:121:25-75
action#android.net.wifi.STATE_CHANGE
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:122:17-71
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:122:25-69
intent-filter#action:name:android.intent.action.MEDIA_EJECT+action:name:android.intent.action.MEDIA_MOUNTED+data:scheme:file
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:125:13-129:29
action#android.intent.action.MEDIA_EJECT
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:126:17-75
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:126:25-73
service#com.bm.atool.service.JobSchedulerService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:133:9-138:43
	android:process
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:138:13-41
	android:enabled
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:136:13-35
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:137:13-36
	android:permission
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:135:13-69
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:134:13-56
service#com.bm.atool.service.WatchDogService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:140:9-145:43
	android:process
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:145:13-41
	android:enabled
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:143:13-35
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:144:13-36
	android:foregroundServiceType
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:142:13-58
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:141:13-52
service#com.bm.atool.service.PlayMusicService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:147:9-149:46
	android:process
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:149:13-44
	android:foregroundServiceType
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:148:13-58
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:147:18-58
service#com.bm.atool.service.ANTAccessibilityService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:150:9-163:19
	android:process
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:154:13-45
	android:enabled
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:152:13-35
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:153:13-36
	android:permission
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:155:13-79
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:151:13-60
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:156:13-158:29
action#android.accessibilityservice.AccessibilityService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:157:17-92
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:157:25-89
meta-data#android.accessibilityservice
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:160:13-162:54
	android:resource
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:162:17-51
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:161:17-60
service#com.bm.atool.service.SocketService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:164:9-180:19
	android:process
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:168:13-38
	android:label
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:167:13-42
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:166:13-37
	android:permission
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:169:13-69
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:165:13-50
intent-filter#action:name:android.net.VpnService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:170:13-172:29
action#android.net.VpnService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:171:17-65
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:171:25-62
meta-data#android.net.VpnService.SUPPORTS_ALWAYS_ON
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:173:13-175:39
	android:value
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:175:17-37
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:174:17-73
property
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:176:13-178:38
	android:value
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:178:17-36
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:177:17-76
service#com.bm.atool.service.NotificationService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:182:9-190:19
	android:label
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:184:13-45
	android:exported
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:186:13-36
	android:permission
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:185:13-87
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:183:13-56
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:187:13-189:29
action#android.service.notification.NotificationListenerService
ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:188:17-99
	android:name
		ADDED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml:188:25-96
uses-sdk
INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml
INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\c78b8bb97859eafdefc774f33c8b74d3\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\c78b8bb97859eafdefc774f33c8b74d3\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5f8dd2566c9eda203a98668cc7ef7b6c\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5f8dd2566c9eda203a98668cc7ef7b6c\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\0cfedff09b8c1a61564a2d292f981376\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\0cfedff09b8c1a61564a2d292f981376\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\9b35a196b101b8bacc2d18286d5f5bc2\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\9b35a196b101b8bacc2d18286d5f5bc2\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\0dd6b178300a398c5583e81b52d2ca55\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\0dd6b178300a398c5583e81b52d2ca55\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\065662a114d9e980de2eb8602aa46dfe\transformed\material-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\065662a114d9e980de2eb8602aa46dfe\transformed\material-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3ee4fad182311aafa4dd3c21e1a92920\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\3ee4fad182311aafa4dd3c21e1a92920\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f99e54b54e0fd99cba6c075cd2f82e8\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f99e54b54e0fd99cba6c075cd2f82e8\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\58d2da51b3a7297353dc91fcce17a305\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\58d2da51b3a7297353dc91fcce17a305\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\80aca24db86cd42a34c9c5f0872fe864\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\80aca24db86cd42a34c9c5f0872fe864\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c520583edb1b643c07b55b9673b3d860\transformed\safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\c520583edb1b643c07b55b9673b3d860\transformed\safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c29a2a5ec5a82e9ac89894f30961df45\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c29a2a5ec5a82e9ac89894f30961df45\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d968e2e921fe73fd429dc1484158ee01\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d968e2e921fe73fd429dc1484158ee01\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d2877f2a4374c5f0ea495c4307113570\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d2877f2a4374c5f0ea495c4307113570\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\234d130502bfda745d1f0ed467eb316b\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\234d130502bfda745d1f0ed467eb316b\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\cd43725c0a9e9dcf8a29cd2176ebb49f\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\cd43725c0a9e9dcf8a29cd2176ebb49f\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\ee8b137033d54aa057823e70d615e647\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\ee8b137033d54aa057823e70d615e647\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\2918f0ebf0c103056ddddb3751eae9f0\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\2918f0ebf0c103056ddddb3751eae9f0\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\eced6a23faf4beccaf4d548a6076d331\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\eced6a23faf4beccaf4d548a6076d331\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\59d7b4103fe863e83f5cfb6d5ff9052e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\59d7b4103fe863e83f5cfb6d5ff9052e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b57708684cd122ed48bec8d25a938f16\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b57708684cd122ed48bec8d25a938f16\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac11b2842053e1c378129bd733121fd7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac11b2842053e1c378129bd733121fd7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\eb0dcaade8c7dcad314192e8fa45f6b0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\eb0dcaade8c7dcad314192e8fa45f6b0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e4efd8f3ae2750354e81a0a9c11c89eb\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e4efd8f3ae2750354e81a0a9c11c89eb\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c0c7ad1cec94fe321cef48655533f965\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c0c7ad1cec94fe321cef48655533f965\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\62bb95ec0abc6811b0683a9ec0dfc95b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\62bb95ec0abc6811b0683a9ec0dfc95b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\06b7d36fc1385742e8e5e7ec844582ed\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\06b7d36fc1385742e8e5e7ec844582ed\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c07ac8b948bf0d861a502c28c7b3c9ef\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c07ac8b948bf0d861a502c28c7b3c9ef\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1bcc2a33a5dad0ef3e775f681fcdb8be\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1bcc2a33a5dad0ef3e775f681fcdb8be\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\e2bc52ec53d3d09eacd7afda93ce0878\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\e2bc52ec53d3d09eacd7afda93ce0878\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\d69ae89571b53efad2154ad8afbb02d7\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\d69ae89571b53efad2154ad8afbb02d7\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4786fc9597915b750c9674f8019f3638\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\4786fc9597915b750c9674f8019f3638\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc7970ebcc9b9f99b26feebcc6a8913f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc7970ebcc9b9f99b26feebcc6a8913f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e42ed84a4862a4d03c59d5391f7980cb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e42ed84a4862a4d03c59d5391f7980cb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31637ce5e5872ca525b4fe826e027ef\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a31637ce5e5872ca525b4fe826e027ef\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\62f38e60f2e215177108a8ff7d00958d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\62f38e60f2e215177108a8ff7d00958d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2db60f4eb854c2f606a4cbc7486eff43\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2db60f4eb854c2f606a4cbc7486eff43\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c0235d1d2d6a040a29e091281f1d0c3a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c0235d1d2d6a040a29e091281f1d0c3a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\93a1855047dd69a012ffb1466c3baec5\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\93a1855047dd69a012ffb1466c3baec5\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\387903bd7373a42a8601c75ca13d7224\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\387903bd7373a42a8601c75ca13d7224\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e92d0134cc811c88c1672a7b95d4b00d\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e92d0134cc811c88c1672a7b95d4b00d\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.github.tiann:FreeReflection:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\dc19d8e7ad6666734d2bbefc174935c3\transformed\FreeReflection-3.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.tiann:FreeReflection:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\dc19d8e7ad6666734d2bbefc174935c3\transformed\FreeReflection-3.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [app.cash.quickjs:quickjs-android:0.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\e000a505335212ee8a988555e4104d45\transformed\quickjs-android-0.9.2\AndroidManifest.xml:5:5-44
MERGED from [app.cash.quickjs:quickjs-android:0.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\e000a505335212ee8a988555e4104d45\transformed\quickjs-android-0.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\7b3ddaef24068abceaf1b8691f09de43\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\7b3ddaef24068abceaf1b8691f09de43\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d9d9cadd3455c73332883d4d92e61ba\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d9d9cadd3455c73332883d4d92e61ba\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8063df366648b779671febaa29771aa6\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8063df366648b779671febaa29771aa6\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\eed3683499b10c60b5c9c6e2a518c54d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\eed3683499b10c60b5c9c6e2a518c54d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0020c061f6caf35e73f20a352bf6cb72\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0020c061f6caf35e73f20a352bf6cb72\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0845b252e397a1fbf50c047ea290ff7d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0845b252e397a1fbf50c047ea290ff7d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\62e6ae9dec43c760cbb39d5d416adced\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\62e6ae9dec43c760cbb39d5d416adced\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\93f78976c09f31387e04ef9100b48ea2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\93f78976c09f31387e04ef9100b48ea2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\50743c170165225d5979ca5903d33e9b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\50743c170165225d5979ca5903d33e9b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f40bd75703256c0d3b710b9eeff99b92\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f40bd75703256c0d3b710b9eeff99b92\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c9a64bbeebbcaee8a717efe3ff041abc\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c9a64bbeebbcaee8a717efe3ff041abc\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\work2025\jianz\code\android-tool-v2\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\eed3683499b10c60b5c9c6e2a518c54d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\eed3683499b10c60b5c9c6e2a518c54d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
